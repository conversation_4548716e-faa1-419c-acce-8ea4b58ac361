<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lakshmi Sai Baking Classes - Home</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <img src="images/logo/baking logo.png" alt="" class="logo-img">
                <div class="brand-text">
                    <span class="main-text">Lakshmi Sai</span>
                    <span class="sub-text">Baking Classes</span>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="about.html">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="services.html">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="gallery.html">Gallery</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="contact.html">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section with Slider -->
    <section id="hero" class="hero-section" style="margin-top: -80px; padding-top: 80px;">
        <div id="heroCarousel" class="carousel slide h-100" data-bs-ride="carousel" data-bs-interval="5000">
            <div class="carousel-indicators">
                <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active"></button>
                <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1"></button>
                <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2"></button>
            </div>
            <div class="carousel-inner h-100">
                <div class="carousel-item active">
                    <div class="hero-slide">
                        <div class="hero-overlay">
                            <div class="container">
                                <div class="row justify-content-center text-center">
                                    <div class="col-lg-10">
                                        <h1 class="hero-title">Lakshmi Sai Baking Classes</h1>
                                        <p class="hero-subtitle">Master the art of professional baking with expert guidance. Learn traditional techniques and modern innovations in our state-of-the-art facilities.</p>
                                        <div class="mt-4">
                                            <a href="services.html" class="btn btn-primary btn-lg me-3">Explore Courses</a>
                                            <a href="contact.html" class="btn btn-outline-light btn-lg">Contact Us</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="carousel-item">
                    <div class="hero-slide">
                        <div class="hero-overlay">
                            <div class="container">
                                <div class="row justify-content-center text-center">
                                    <div class="col-lg-10">
                                        <h1 class="hero-title">June 14th & 15th Workshop</h1>
                                        <p class="hero-subtitle">Join our exclusive Cake & Cake Decoration Workshop. Learn professional techniques from industry experts. Limited seats available - Registration mandatory!</p>
                                        <div class="mt-4">
                                            <a href="contact.html" class="btn btn-primary btn-lg me-3">Register Now</a>
                                            <a href="gallery.html" class="btn btn-outline-light btn-lg">View Gallery</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="carousel-item">
                    <div class="hero-slide">
                        <div class="hero-overlay">
                            <div class="container">
                                <div class="row justify-content-center text-center">
                                    <div class="col-lg-10">
                                        <h1 class="hero-title">Professional Training Excellence</h1>
                                        <p class="hero-subtitle">Experience hands-on learning with both gas and oven baking methods. Every student receives comprehensive printed materials and personalized guidance.</p>
                                        <div class="mt-4">
                                            <a href="services.html" class="btn btn-primary btn-lg me-3">Enroll Now</a>
                                            <a href="about.html" class="btn btn-outline-light btn-lg">Learn More</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon"></span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon"></span>
            </button>
        </div>
    </section>

    <!-- Welcome Section -->
    <section class="welcome-section py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="welcome-content">
                        <h2 class="section-title">Welcome to Lakshmi Sai Baking Classes</h2>
                        <p class="lead">Training at the best with professional techniques</p>
                        <p>At Lakshmi Sai Baking Classes, we provide comprehensive hands-on training in baking and cake decoration. Our special workshop on June 14th & 15th includes everything from basic sponge cakes to advanced decoration techniques. Registration is mandatory as seats are limited!</p>
                        <div class="welcome-stats row mt-4">
                            <div class="col-4 text-center">
                                <h3 class="stat-number">500+</h3>
                                <p class="stat-label">Students Trained</p>
                            </div>
                            <div class="col-4 text-center">
                                <h3 class="stat-number">15+</h3>
                                <p class="stat-label">Courses Offered</p>
                            </div>
                            <div class="col-4 text-center">
                                <h3 class="stat-number">10+</h3>
                                <p class="stat-label">Years Experience</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="welcome-image">
                        <div style="height: 450px; background: url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&crop=center') center/cover; border-radius: 20px; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(26, 26, 46, 0.3), rgba(212, 175, 55, 0.2)); display: flex; align-items: center; justify-content: center; border-radius: 20px;">
                                <div class="text-center text-white">
                                    <i class="fas fa-graduation-cap fa-4x mb-3" style="color: #d4af37;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Workshop Details Section -->
    <section class="workshop-section py-5" style="background: linear-gradient(135deg, #E8F8F5 0%, #ffffff 100%);">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title" style="color: #20B2AA;">🎂 Special Workshop: June 14th & 15th 🍰</h2>
                    <p class="section-subtitle">Cake & Cake Decoration Workshop</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card h-100" style="border: 2px solid #20B2AA; border-radius: 25px; box-shadow: 0 8px 30px rgba(32, 178, 170, 0.15);">
                        <div class="card-header text-white" style="background: linear-gradient(135deg, #20B2AA 0%, #17A2B8 100%); border-radius: 23px 23px 0 0;">
                            <h5 class="mb-0"><i class="fas fa-birthday-cake me-2"></i>Workshop Includes</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Basic Sponge cake</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Dry fruit cake</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Plum cake</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Pineapple cake</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Chocolate cake</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Fresh fruit cake</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Cupcakes & Muffins</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Fresh cream icing</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Chocolate ganache</li>
                                <li><i class="fas fa-check me-2" style="color: #20B2AA;"></i>Glazing and Icing techniques</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card h-100" style="border: 2px solid #FF6B6B; border-radius: 25px; box-shadow: 0 8px 30px rgba(255, 107, 107, 0.15);">
                        <div class="card-header text-white" style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%); border-radius: 23px 23px 0 0;">
                            <h5 class="mb-0"><i class="fas fa-star me-2"></i>Specialty Items</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check me-2" style="color: #FF6B6B;"></i>Black forest pastry</li>
                                <li><i class="fas fa-check me-2" style="color: #FF6B6B;"></i>Butterscotch pastry</li>
                                <li><i class="fas fa-check me-2" style="color: #FF6B6B;"></i>Chocolate truffle</li>
                                <li><i class="fas fa-check me-2" style="color: #FF6B6B;"></i>Pineapple pastry</li>
                            </ul>
                            <div class="mt-4">
                                <h6 style="color: #FFA726;">Special Features:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-fire me-2" style="color: #FF6B6B;"></i>Baking on gas & oven both</li>
                                    <li><i class="fas fa-book me-2" style="color: #20B2AA;"></i>Printed notes provided</li>
                                    <li><i class="fas fa-hands me-2" style="color: #17A2B8;"></i>Hands-on training</li>
                                    <li><i class="fas fa-users me-2" style="color: #28a745;"></i>Group discounts available</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-4">
                <div class="col-12 text-center">
                    <div class="alert d-inline-block" style="background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%); color: white; border: none; border-radius: 25px; box-shadow: 0 8px 30px rgba(255, 107, 107, 0.2);">
                        <h5 class="mb-2"><i class="fas fa-exclamation-triangle me-2"></i>Registration Mandatory</h5>
                        <p class="mb-0">Hurry up! 🏃‍♀️🏃‍♀️ Seats are limited</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Services Preview -->
    <section class="services-preview py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">Our Popular Courses</h2>
                    <p class="section-subtitle">Discover the perfect course to start your baking journey</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <h4>Basic Baking</h4>
                        <p>Perfect for beginners. Learn fundamental techniques and create delicious treats.</p>
                        <a href="services.html" class="btn btn-outline-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-cookie-bite"></i>
                        </div>
                        <h4>Pastry Arts</h4>
                        <p>Master the art of pastry making with advanced techniques and creative designs.</p>
                        <a href="services.html" class="btn btn-outline-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="service-card">
                        <div class="service-icon">
                            <i class="fas fa-bread-slice"></i>
                        </div>
                        <h4>Artisan Bread</h4>
                        <p>Learn traditional and modern bread making techniques from scratch.</p>
                        <a href="services.html" class="btn btn-outline-primary">Learn More</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Preview -->
    <section class="gallery-preview py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">Student Creations</h2>
                    <p class="section-subtitle">See the amazing work our students create</p>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="gallery-item">
                        <div style="height: 280px; background: url('https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400&h=300&fit=crop&crop=center') center/cover; border-radius: 25px;"></div>
                        <div class="gallery-overlay">
                            <a href="gallery.html" class="btn btn-light">View Gallery</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="gallery-item">
                        <div style="height: 280px; background: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&crop=center') center/cover; border-radius: 25px;"></div>
                        <div class="gallery-overlay">
                            <a href="gallery.html" class="btn btn-light">View Gallery</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="gallery-item">
                        <div style="height: 280px; background: url('https://images.unsplash.com/photo-1574085733277-851d9d856a3a?w=400&h=300&fit=crop&crop=center') center/cover; border-radius: 25px;"></div>
                        <div class="gallery-overlay">
                            <a href="gallery.html" class="btn btn-light">View Gallery</a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="gallery-item">
                        <div style="height: 280px; background: url('https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400&h=300&fit=crop&crop=center') center/cover; border-radius: 25px;"></div>
                        <div class="gallery-overlay">
                            <a href="gallery.html" class="btn btn-light">View Gallery</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-logo">
                        <img src="images/logo/logo-footer.png" alt="" class="logo-img">
                        <div class="brand-text">
                            <span class="main-text">Lakshmi Sai</span>
                            <span class="sub-text">Baking Classes</span>
                        </div>
                    </div>
                    <p>Training at the best! Learn professional baking techniques with hands-on experience and expert guidance.</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6>Quick Links</h6>
                    <ul class="footer-links">
                        <li><a href="index.html">Home</a></li>
                        <li><a href="about.html">About</a></li>
                        <li><a href="services.html">Services</a></li>
                        <li><a href="gallery.html">Gallery</a></li>
                        <li><a href="contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6>Contact Info</h6>
                    <ul class="contact-info">
                        <li><i class="fas fa-map-marker-alt"></i> Sri Vyshnavi Indralok Phase II, Flat 101, BWSSB Road, Challaghatta, Bangalore - 560017</li>
                        <li><i class="fas fa-phone"></i> 9916192449</li>
                        <li><i class="fas fa-user"></i> Contact: Indu</li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h6>Newsletter</h6>
                    <p>Subscribe to get updates on new courses and events.</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Your email">
                            <button class="btn btn-primary" type="submit">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>
            <hr class="my-4">
            <div class="row">
                <div class="col-12 text-center">
                    <p>&copy; 2024 Lakshmi Sai Baking Classes. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/main.js"></script>
</body>
</html>
