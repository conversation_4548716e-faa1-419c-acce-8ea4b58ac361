/* Modern Fresh Theme - <PERSON> Sai <PERSON>king Classes */

/* Fresh Modern Color Palette */
:root {
    --primary-teal: #20B2AA;
    --primary-blue: #17A2B8;
    --accent-coral: #FF6B6B;
    --accent-salmon: #FF8E53;
    --warm-orange: #FFA726;
    --pure-white: #ffffff;
    --light-gray: #f8f9fa;
    --medium-gray: #6c757d;
    --dark-gray: #2c3e50;
    --success-green: #28a745;
    --soft-mint: #E8F8F5;
    --light-coral: #FFE5E5;
    --gradient-primary: linear-gradient(135deg, #20B2AA 0%, #17A2B8 100%);
    --gradient-accent: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
    --gradient-warm: linear-gradient(135deg, #FFA726 0%, #FF8E53 100%);
    --gradient-overlay: linear-gradient(135deg, rgba(32, 178, 170, 0.9) 0%, rgba(23, 162, 184, 0.8) 100%);
    --shadow-light: 0 2px 15px rgba(32, 178, 170, 0.1);
    --shadow-medium: 0 8px 30px rgba(32, 178, 170, 0.15);
    --shadow-heavy: 0 15px 50px rgba(32, 178, 170, 0.2);
    --shadow-coral: 0 8px 30px rgba(255, 107, 107, 0.2);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.7;
    color: var(--dark-gray);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #E8F8F5 100%);
    overflow-x: hidden;
}

/* Modern Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: var(--dark-gray);
    letter-spacing: -0.02em;
    line-height: 1.2;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.8rem; }
h3 { font-size: 2.2rem; }
h4 { font-size: 1.8rem; }
h5 { font-size: 1.4rem; }
h6 { font-size: 1.2rem; }

/* Section Styling */
.section-title {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--dark-gray);
    position: relative;
    text-align: center;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--medium-gray);
    margin-bottom: 3rem;
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
}

/* Modern Coral Gradient Navigation */
.navbar {
    background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 50%, #FFA726 100%);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 30px rgba(255, 107, 107, 0.3);
    transition: all 0.4s ease;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.navbar.scrolled {
    background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 50%, #FFA726 100%);
    box-shadow: 0 15px 50px rgba(255, 107, 107, 0.4);
    padding: 0.5rem 0;
}

.navbar-brand {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    font-weight: 700;
    color: var(--pure-white) !important;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.navbar-brand .logo-img {
    height: 60px;
    width: auto;
    margin-right: 15px;
    filter: drop-shadow(0 2px 4px rgba(255, 255, 255, 0.3)) contrast(1.1) brightness(1.2);
}

.navbar-brand .brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
}

.navbar-brand .brand-text .main-text {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #20B2AA 0%, #17A2B8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.navbar-brand .brand-text .sub-text {
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    letter-spacing: 0.5px;
}

.nav-link {
    font-weight: 600;
    color: var(--pure-white) !important;
    margin: 0 15px;
    padding: 10px 0 !important;
    position: relative;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, #20B2AA 0%, #17A2B8 100%);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-link:hover,
.nav-link.active {
    color: #20B2AA !important;
}

/* Professional Hero Section */
.hero-section {
    height: 50vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    margin-top: 0;
    padding-top: 0;
}

.carousel-item {
    height: 100vh;
}

.hero-slide {
    height: 100vh;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
}

/* Professional Background Images */
.carousel-item:nth-child(1) .hero-slide {
    background-image: url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1920&h=1080&fit=crop&crop=center');
}

.carousel-item:nth-child(2) .hero-slide {
    background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1920&h=1080&fit=crop&crop=center');
}

.carousel-item:nth-child(3) .hero-slide {
    background-image: url('https://images.unsplash.com/photo-1574085733277-851d9d856a3a?w=1920&h=1080&fit=crop&crop=center');
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-title {
    font-size: 4.5rem;
    font-weight: 700;
    color: var(--pure-white);
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    letter-spacing: -0.02em;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    font-weight: 400;
    max-width: 600px;
    line-height: 1.6;
}

/* Modern Fresh Buttons */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    color: var(--pure-white);
    font-weight: 600;
    padding: 15px 35px;
    border-radius: 25px;
    transition: all 0.4s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
    background: var(--gradient-accent);
}

.btn-outline-primary {
    border: 2px solid var(--primary-teal);
    color: var(--primary-teal);
    background: transparent;
    font-weight: 600;
    padding: 13px 33px;
    border-radius: 25px;
    transition: all 0.4s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    border-color: var(--primary-teal);
    color: var(--pure-white);
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    color: var(--pure-white);
    background: transparent;
    font-weight: 600;
    padding: 13px 33px;
    border-radius: 25px;
    transition: all 0.4s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--pure-white);
    color: var(--pure-white);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Fresh Modern Welcome Section */
.welcome-section {
    background: var(--gradient-primary);
    padding: 120px 0;
    position: relative;
    overflow: hidden;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1920&h=1080&fit=crop&crop=center');
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    z-index: 1;
}

.welcome-section .container {
    position: relative;
    z-index: 2;
}

.welcome-content h2 {
    color: var(--pure-white);
    margin-bottom: 2.5rem;
    font-size: 3rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.welcome-content .lead {
    color: var(--warm-orange);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

.welcome-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.welcome-stats {
    margin-top: 3rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    background: var(--gradient-warm);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: var(--pure-white);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.welcome-image {
    position: relative;
}

.welcome-image::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: -20px;
    bottom: -20px;
    background: var(--gradient-warm);
    border-radius: 20px;
    z-index: -1;
    opacity: 0.1;
}

.welcome-image img,
.welcome-image > div {
    border-radius: 20px;
    box-shadow: var(--shadow-heavy);
    transition: transform 0.4s ease;
}

.welcome-image:hover img,
.welcome-image:hover > div {
    transform: translateY(-10px);
}

/* Fresh Modern Services Preview */
.services-preview {
    background: var(--soft-mint);
    padding: 120px 0;
    position: relative;
    overflow: hidden;
}

.services-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1920&h=1080&fit=crop&crop=center');
    background-size: cover;
    background-position: center;
    opacity: 0.03;
    z-index: 1;
}

.services-preview .container {
    position: relative;
    z-index: 2;
}

.services-preview .section-title {
    color: var(--dark-gray);
}

.services-preview .section-title::after {
    background: var(--gradient-primary);
}

.services-preview .section-subtitle {
    color: var(--medium-gray);
}

.service-card {
    background: rgba(255, 255, 255, 1);
    backdrop-filter: blur(20px);
    padding: 3rem 2rem;
    border-radius: 25px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: all 0.4s ease;
    height: 100%;
    border: 1px solid rgba(32, 178, 170, 0.1);
}

.service-card:hover {
    transform: translateY(-15px);
    box-shadow: var(--shadow-coral);
    background: rgba(255, 255, 255, 1);
    border-color: var(--accent-coral);
}

.service-icon {
    width: 100px;
    height: 100px;
    background: var(--gradient-primary);
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    transition: all 0.4s ease;
}

.service-card:hover .service-icon {
    background: var(--gradient-accent);
    transform: scale(1.1);
}

.service-icon i {
    font-size: 2.5rem;
    color: var(--pure-white);
}

.service-card h4 {
    color: var(--dark-gray);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.service-card p {
    color: var(--medium-gray);
    margin-bottom: 2rem;
    line-height: 1.7;
}

/* Fresh Modern Gallery Preview */
.gallery-preview {
    padding: 120px 0;
    background: var(--light-coral);
    position: relative;
    overflow: hidden;
}

.gallery-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://images.unsplash.com/photo-1574085733277-851d9d856a3a?w=1920&h=1080&fit=crop&crop=center');
    background-size: cover;
    background-position: center;
    opacity: 0.03;
    z-index: 1;
}

.gallery-preview .container {
    position: relative;
    z-index: 2;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 25px;
    box-shadow: var(--shadow-light);
    transition: all 0.4s ease;
    background: var(--pure-white);
}

.gallery-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-coral);
}

.gallery-item img,
.gallery-item > div {
    width: 100%;
    height: 280px;
    object-fit: cover;
    transition: all 0.4s ease;
    border-radius: 25px;
}

.gallery-item:hover img,
.gallery-item:hover > div {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 25px;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay .btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid var(--pure-white);
    color: var(--pure-white);
    backdrop-filter: blur(10px);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: 25px;
}

/* Modern Fresh Footer */
.footer {
    background: var(--dark-gray);
    color: var(--pure-white);
    padding: 80px 0 30px;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1920&h=1080&fit=crop&crop=center');
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    z-index: 1;
}

.footer .container {
    position: relative;
    z-index: 2;
}

.footer h5,
.footer h6 {
    color: var(--warm-orange);
    margin-bottom: 1.5rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo .logo-img {
    height: 70px;
    width: auto;
    margin-right: 20px;
    filter: brightness(1.2);
}

.footer-logo .brand-text {
    display: flex;
    flex-direction: column;
    line-height: 1.1;
}

.footer-logo .brand-text .main-text {
    font-size: 1.8rem;
    font-weight: 700;
    background: var(--gradient-warm);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-logo .brand-text .sub-text {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    letter-spacing: 0.5px;
}

.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.5rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--warm-orange);
}

.contact-info {
    list-style: none;
    padding: 0;
}

.contact-info li {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.contact-info i {
    color: var(--warm-orange);
    margin-right: 10px;
    width: 20px;
}

.social-links {
    margin-top: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background-color: var(--primary-teal);
    color: var(--pure-white);
    text-align: center;
    line-height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background-color: var(--accent-coral);
    transform: translateY(-3px);
}

.newsletter-form .form-control {
    border: none;
    border-radius: 25px 0 0 25px;
    padding: 10px 15px;
}

.newsletter-form .btn {
    border-radius: 0 25px 25px 0;
    padding: 10px 20px;
}

/* Modern Workshop Section */
.workshop-section {
    position: relative;
    overflow: hidden;
}

.workshop-section .card {
    transition: all 0.4s ease;
}

.workshop-section .card:hover {
    transform: translateY(-10px);
}

.workshop-section .card-header {
    font-weight: 600;
    border: none;
}

.workshop-section .card-body {
    padding: 2rem;
}

.workshop-section .card-body li {
    padding: 0.5rem 0;
    font-weight: 500;
    color: var(--dark-gray);
}

.workshop-section .alert {
    font-weight: 600;
    padding: 1.5rem 2rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Professional Page Header */
.page-header {
    background: var(--gradient-primary);
    padding: 150px 0 100px;
    margin-top: 80px;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=1920&h=1080&fit=crop&crop=center');
    background-size: cover;
    background-position: center;
    opacity: 0.1;
    z-index: 1;
}

.page-header .container {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: 4rem;
    color: var(--pure-white);
    margin-bottom: 1.5rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.page-subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
    font-weight: 400;
}

/* About Page Styles */
.our-story {
    background-color: var(--white);
}

.story-content .lead {
    color: var(--gold);
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

.highlight-item {
    text-align: center;
    padding: 1rem;
}

.highlight-item i {
    font-size: 2rem;
    color: var(--gold);
    margin-bottom: 1rem;
}

.highlight-item h5 {
    color: var(--chocolate-brown);
    margin-bottom: 0.5rem;
}

.highlight-item p {
    font-size: 0.9rem;
    color: var(--dark-brown);
}

.mission-card,
.vision-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    height: 100%;
    text-align: center;
}

.card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--light-pink), var(--gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.card-icon i {
    font-size: 2rem;
    color: var(--chocolate-brown);
}

.value-card {
    padding: 2rem 1rem;
    transition: all 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
}

.value-icon {
    width: 70px;
    height: 70px;
    background: var(--light-pink);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.value-icon i {
    font-size: 1.5rem;
    color: var(--chocolate-brown);
}

.team-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.team-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.team-info {
    padding: 1.5rem;
    text-align: center;
}

.team-role {
    color: var(--gold);
    font-weight: 500;
    margin-bottom: 1rem;
}

.team-social {
    margin-top: 1rem;
}

.team-social a {
    display: inline-block;
    width: 35px;
    height: 35px;
    background-color: var(--light-pink);
    color: var(--chocolate-brown);
    text-align: center;
    line-height: 35px;
    border-radius: 50%;
    margin: 0 5px;
    transition: all 0.3s ease;
}

.team-social a:hover {
    background-color: var(--gold);
    transform: translateY(-2px);
}

/* Services Page Styles */
.course-categories {
    background-color: var(--white);
}

.category-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    text-align: center;
    height: 100%;
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--light-pink), var(--gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.category-icon i {
    font-size: 2rem;
    color: var(--chocolate-brown);
}

.category-features {
    list-style: none;
    padding: 0;
    margin-top: 1.5rem;
}

.category-features li {
    padding: 0.5rem 0;
    color: var(--dark-brown);
    position: relative;
    padding-left: 1.5rem;
}

.category-features li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--gold);
    font-weight: bold;
}

.course-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.course-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.course-card.featured {
    border: 2px solid var(--gold);
}

.course-image {
    position: relative;
    overflow: hidden;
}

.course-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.course-card:hover .course-image img {
    transform: scale(1.05);
}

.course-level {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--chocolate-brown);
    color: var(--white);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--gold);
    color: var(--chocolate-brown);
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.course-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.course-content h4 {
    color: var(--chocolate-brown);
    margin-bottom: 1rem;
}

.course-content p {
    color: var(--dark-brown);
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.course-details {
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--dark-brown);
}

.detail-item i {
    color: var(--gold);
    margin-right: 10px;
    width: 16px;
}

.course-price {
    text-align: center;
    margin-bottom: 1.5rem;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--chocolate-brown);
}

.price-note {
    font-size: 0.9rem;
    color: var(--dark-brown);
    display: block;
}

.feature-item {
    padding: 2rem 1rem;
    transition: all 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--light-pink);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.feature-icon i {
    font-size: 1.5rem;
    color: var(--chocolate-brown);
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
}

.modal-header {
    background: linear-gradient(45deg, var(--chocolate-brown), var(--gold));
    color: var(--white);
    border-radius: 15px 15px 0 0;
}

.modal-title {
    color: var(--white);
}

.btn-close {
    filter: invert(1);
}

.form-label {
    color: var(--chocolate-brown);
    font-weight: 500;
}

.form-control,
.form-select {
    border: 2px solid var(--cream-white);
    border-radius: 10px;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--gold);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
}

/* Gallery Page Styles */
.gallery-filter {
    background-color: var(--light-gray);
}

.filter-buttons {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-buttons .btn {
    margin: 5px;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-buttons .btn.active {
    background-color: var(--chocolate-brown);
    border-color: var(--chocolate-brown);
    color: var(--white);
}

.gallery-grid {
    background-color: var(--white);
}

.gallery-card {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
}

.gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.gallery-card img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.gallery-card:hover img {
    transform: scale(1.1);
}

/* Portrait Gallery Card Styles */
.portrait-card {
    aspect-ratio: 3/4;
    min-height: 400px;
}

.portrait-card img,
.portrait-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
}

.portrait-card .text-center {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Responsive portrait adjustments */
@media (max-width: 768px) {
    .portrait-card {
        min-height: 300px;
        aspect-ratio: 3/4;
    }
}

@media (min-width: 1200px) {
    .portrait-card {
        min-height: 450px;
    }
}

/* Legacy Landscape Gallery Card Styles (for backward compatibility) */
.landscape-card {
    aspect-ratio: 16/9;
    min-height: 320px;
}

.landscape-card img,
.landscape-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    border-radius: 15px;
}

.landscape-card .text-center {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Responsive landscape adjustments */
@media (max-width: 768px) {
    .landscape-card {
        min-height: 250px;
        aspect-ratio: 4/3;
    }
}

@media (min-width: 1200px) {
    .landscape-card {
        min-height: 350px;
    }
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(139, 69, 19, 0.9), rgba(255, 215, 0, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.gallery-card:hover .gallery-overlay {
    opacity: 1;
}

.gallery-content {
    text-align: center;
    color: var(--white);
    padding: 1rem;
}

.gallery-content h5 {
    color: var(--white);
    margin-bottom: 0.5rem;
}

.gallery-content p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

/* Contact Page Styles */
.contact-info-section {
    background-color: var(--white);
}

.contact-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(139, 69, 19, 0.2);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(45deg, var(--light-pink), var(--gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.contact-icon i {
    font-size: 2rem;
    color: var(--chocolate-brown);
}

.contact-card h4 {
    color: var(--chocolate-brown);
    margin-bottom: 1rem;
}

.contact-card p {
    color: var(--dark-brown);
    margin-bottom: 1.5rem;
}

.contact-form-container {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
}

.map-container {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(139, 69, 19, 0.1);
}

.map-wrapper {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(139, 69, 19, 0.2);
}

.info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.info-item i {
    margin-right: 15px;
    margin-top: 5px;
    font-size: 1.2rem;
}

.info-content h6 {
    color: var(--chocolate-brown);
    margin-bottom: 0.5rem;
}

.info-content p {
    color: var(--dark-brown);
    font-size: 0.9rem;
    margin: 0;
}

.faq-section {
    background-color: var(--white);
}

.accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(139, 69, 19, 0.1);
}

.accordion-button {
    background-color: var(--cream-white);
    color: var(--chocolate-brown);
    font-weight: 500;
    border: none;
    padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
    background-color: var(--gold);
    color: var(--chocolate-brown);
    box-shadow: none;
}

.accordion-button:focus {
    box-shadow: none;
    border-color: transparent;
}

.accordion-body {
    background-color: var(--white);
    color: var(--dark-brown);
    padding: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .navbar-brand {
        font-size: 1.5rem;
    }

    .welcome-stats .col-4 {
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .mission-card,
    .vision-card {
        padding: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .gallery-item img {
        height: 200px;
    }

    .team-image img {
        height: 200px;
    }
}
